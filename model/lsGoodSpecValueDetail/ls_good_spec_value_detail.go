package lsGoodSpecValueDetail

type LsGoodSpecValueDetail struct {
	ID            int     `gorm:"primaryKey;autoIncrement"` // 主键，自增
	GoodSpecValue string  `gorm:"type:varchar(100);not null;comment:规格值（如：黑色｜36｜牛皮）"`
	GoodSpecImage string  `gorm:"type:varchar(100);comment:规格照片"`
	GoodSpecPrice float64 `gorm:"type:decimal(10,2);comment:售价"`
	MarketPrice   float64 `gorm:"type:decimal(10,2);comment:划线价"`
	CostPrice     float64 `gorm:"type:decimal(10,2);comment:成本价"`
	Stock         int     `gorm:"comment:库存"`
	Volume        float64 `gorm:"type:float(10,2);comment:体积"`
	Weight        float64 `gorm:"type:float(10,2);comment:重量"`
	BarCode       string  `gorm:"type:varchar(20);comment:条码"`
	GoodID        uint    `gorm:"type:bigint;comment:商品ID"`
	GoodSpecName  string  `gorm:"type:varchar(50);comment:规格名称"`
}

func (LsGoodSpecValueDetail) TableName() string {
	return "ls_good_spec_value_detail"
}
