
// 自动生成模板LsBrand
package ls_brand
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// lsBrand表 结构体  LsBrand
type LsBrand struct {
    global.GVA_MODEL
    BrandName  *string `json:"brandName" form:"brandName" gorm:"uniqueIndex;column:brand_name;comment:品牌名称;size:20;" binding:"required"`  //品牌名称
    BrandIcon  *string `json:"brandIcon" form:"brandIcon" gorm:"column:brand_icon;comment:品牌标志;size:100;" binding:"required"`  //品牌标志
    Sort  *int `json:"sort" form:"sort" gorm:"column:sort;comment:排序;size:10;"`  //排序
    Enable  *string `json:"enable" form:"enable" gorm:"default:false;column:enable;comment:开关;size:20;"`  //开关
    CreatedBy  uint   `gorm:"column:created_by;comment:创建者"`
    UpdatedBy  uint   `gorm:"column:updated_by;comment:更新者"`
    DeletedBy  uint   `gorm:"column:deleted_by;comment:删除者"`
}


// TableName lsBrand表 LsBrand自定义表名 ls_brand
func (LsBrand) TableName() string {
    return "ls_brand"
}





