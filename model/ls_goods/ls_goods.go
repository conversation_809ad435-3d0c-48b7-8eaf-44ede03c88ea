// 自动生成模板LsGoods
package ls_goods

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// lsGoods表 结构体  LsGoods
type LsGoods struct {
	global.GVA_MODEL
	GoodType              *string `json:"goodType" form:"goodType" gorm:"column:good_type;comment:商品类型;size:20;" binding:"required"`                       //商品类型
	GoodCode              string  `json:"goodCode" form:"goodCode" gorm:"column:good_code;comment:商品类型;size:80;" binding:"required"`                       //商品类型
	GoodName              *string `json:"goodName" form:"goodName" gorm:"index;column:good_name;comment:商品名称;size:100;" binding:"required"`                //商品名称
	VideoEnable           *bool   `json:"videoEnable" form:"videoEnable" gorm:"default:0;column:video_enable;comment:0 表示不展示 1表示展示;"`                      //0 表示不展示 1表示展示
	VideoSource           *string `json:"videoSource" form:"videoSource" gorm:"column:video_source;comment:视频来源;size:20;"`                                 //视频来源
	VideoUrl              *string `json:"videoUrl" form:"videoUrl" gorm:"column:video_url;comment:视频链接;size:100;"`                                         //视频链接
	VideoCover            *string `json:"videoCover" form:"videoCover" gorm:"column:video_cover;comment:视频封面;size:100;"`                                   //视频封面
	GoodBrandId           *int    `json:"goodBrandId" form:"goodBrandId" gorm:"column:good_brand_id;comment:品牌ID;size:20;" binding:"required"`             //品牌ID
	GoodUnit              *string `json:"goodUnit" form:"goodUnit" gorm:"column:good_unit;comment:单位;size:20;" binding:"required"`                         //单位
	GoodSupplier          *int    `json:"goodSupplier" form:"goodSupplier" gorm:"column:good_supplier;comment:供货商;size:10;" binding:"required"`            //供货商
	GoodSpec              *string `json:"goodSpec" form:"goodSpec" gorm:"column:good_spec;comment:商品规格;size:20;" binding:"required"`                       //商品规格
	Logistics             *string `json:"logistics" form:"logistics" gorm:"column:logistics;comment:物流支持;size:20;" binding:"required"`                     //物流支持
	ShippingFee           *string `json:"shippingFee" form:"shippingFee" gorm:"column:shipping_fee;comment:运费设置;size:20;" binding:"required"`              //运费设置
	ShippingFeeTemplement *int    `json:"shippingFeeTemplement" form:"shippingFeeTemplement" gorm:"column:shipping_fee_templement;comment:运费模版;size:191;"` //运费模版
	GoodDetail            *string `json:"goodDetail" form:"goodDetail" gorm:"column:good_detail;comment:商品详情;type:text;"`                                  //商品详情
	StockLimit            *int    `json:"stockLimit" form:"stockLimit" gorm:"column:stock_limit;comment:库存预警;size:19;"`                                    //库存预警
	VirtualSale           *int    `json:"virtualSale" form:"virtualSale" gorm:"column:virtual_sale;comment:虚拟销量;size:19;"`                                 //虚拟销量
	VirtualPageView       *int    `json:"virtualPageView" form:"virtualPageView" gorm:"column:virtual_page_view;comment:虚拟浏览量;size:19;"`                   //虚拟浏览量
	ServiceGuarantee      *string `json:"serviceGuarantee" form:"serviceGuarantee" gorm:"column:service_guarantee;comment:服务保障;size:20;"`                  //服务保障
	QuotaCode             *string `json:"quotaCode" form:"quotaCode" gorm:"column:quota_code;comment:限购类型;size:29;"`                                       //限购类型
	QuotaCount            *int    `json:"quotaCount" form:"quotaCount" gorm:"column:quota_count;comment:限购数量;size:19;"`                                    //限购数量
	GoodStatus            *string `json:"goodStatus" form:"goodStatus" gorm:"column:good_status;comment:商品状态;size:20;" binding:"required"`                 //商品状态
	CreatedBy             uint    `gorm:"column:created_by;comment:创建者"`
	UpdatedBy             uint    `gorm:"column:updated_by;comment:更新者"`
	DeletedBy             uint    `gorm:"column:deleted_by;comment:删除者"`
}

// TableName lsGoods表 LsGoods自定义表名 ls_goods
func (LsGoods) TableName() string {
	return "ls_goods"
}
