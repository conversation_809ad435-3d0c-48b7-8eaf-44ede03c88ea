package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"time"
)

type LsGoodsSearch struct {
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	GoodName       *string    `json:"goodName" form:"goodName" `
	request.PageInfo
}

type Goods struct {
	GoodBrandID           *int         `json:"goodBrandId" form:"goodBrandId"`
	GoodDetail            *string      `json:"goodDetail" form:"goodDetail"`
	GoodName              *string      `json:"goodName" form:"goodName"`
	GoodSpec              *string      `json:"goodSpec" form:"goodSpec"`
	GoodStatus            *string      `json:"goodStatus" form:"goodStatus"`
	GoodSupplier          *int         `json:"goodSupplier" form:"goodSupplier"`
	GoodType              *string      `json:"goodType" form:"goodType"`
	GoodUnit              *string      `json:"goodUnit" form:"goodUnit"`
	Logistics             *string      `json:"logistics" form:"logistics"`
	MenuBtn               []SpecDetail `json:"menuBtn" form:"menuBtn"`
	QuotaCode             *string      `json:"quotaCode" form:"quotaCode"`
	ServiceGuarantee      *string      `json:"serviceGuarantee" form:"serviceGuarantee"`
	ShippingFee           *string      `json:"shippingFee" form:"shippingFee"`
	ShippingFeeTemplement *int         `json:"shippingFeeTemplement" form:"shippingFeeTemplement"`
	VideoCover            *string      `json:"videoCover" form:"videoCover"`
	VideoEnable           *bool        `json:"videoEnable" form:"videoEnable"`
	VideoSource           *string      `json:"videoSource" form:"videoSource"`
	VideoURL              *string      `json:"videoUrl" form:"videoUrl"`
	StockLimit            *int         `json:"stockLimit" form:"stockLimit"`
	QuotaCount            *int         `json:"quotaCount" form:"quotaCount"`
	VirtualPageView       *int         `json:"virtualPageView" form:"virtualPageView"`
	VirtualSale           *int         `json:"virtualSale" form:"virtualSale"`
	CreatedBy             uint         `json:"created_by" form:"created_by"`
	UpdatedBy             uint         `json:"updated_by" form:"updated_by"`
	DeletedBy             uint         `json:"deleted_by" form:"deleted_by"`
}

type SpecDetail struct {
	GoodSpecValue string  `json:"goodSpecValue" form:"goodSpecValue"`
	GoodSpecImage string  `json:"goodSpecImage" form:"goodSpecImage"`
	Key           string  `json:"key" form:"key"`
	GoodPrice     float64 `json:"goodPrice" form:"goodPrice"`
	MarketPrice   float64 `json:"marketPrice" form:"marketPrice"`
	CostPrice     float64 `json:"costPrice" form:"costPrice"`
	Stock         int     `json:"stock" form:"stock"`
	Volume        float64 `json:"volume" form:"volume"`
	Weight        float64 `json:"weight" form:"weight"`
	BarCode       string  `json:"barCode" form:"barCode"`
}
