// 自动生成模板LsSupplier
package li_supplier

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// lsSupplier表 结构体  LsSupplier
type LsSupplier struct {
	global.GVA_MODEL
	SupplierCode   string  `json:"supplierCode" form:"supplierCode" gorm:"index;column:supplier_code;comment:供应商编号;size:50;" `                    //供应商 名称
	SupplierName   *string `json:"supplierName" form:"supplierName" gorm:"index;column:supplier_name;comment:供应商 名称;size:20;" binding:"required"` //供应商 名称
	SupplierUser   *string `json:"supplierUser" form:"supplierUser" gorm:"index;column:supplier_user;comment:联系人;size:20;" binding:"required"`      //联系人
	SupplierMobile *string `json:"supplierMobile" form:"supplierMobile" gorm:"column:supplier_mobile;comment:手机号;size:20;" binding:"required"`      //手机号
	SupplierTel    *string `json:"supplierTel" form:"supplierTel" gorm:"column:supplier_tel;comment:座机;size:20;"`                                    //座机
	SupplierEmail  *string `json:"supplierEmail" form:"supplierEmail" gorm:"index;column:supplier_email;comment:邮箱;size:20;" binding:"required"`     //邮箱
	Address        *string `json:"address" form:"address" gorm:"column:address;comment:地址;size:50;"`                                                 //地址
	Province       string  `json:"province" form:"province" gorm:"column:province;comment:省;size:50;"`                                                //省
	City           string  `json:"city" form:"city" gorm:"column:city;comment:市;size:50;"`                                                            //市
	Area           string  `json:"area" form:"area" gorm:"column:area;comment:区;size:50;"`                                                            //区
	BankAccount    *string `json:"bankAccount" form:"bankAccount" gorm:"index;column:bank_account;comment:银行账号;size:30;" binding:"required"`       //银行账号
	BankSource     *string `json:"bankSource" form:"bankSource" gorm:"column:bank_source;comment:开户行;size:20;" binding:"required"`                  //开户行
	CardUser       *string `json:"cardUser" form:"cardUser" gorm:"column:card_user;comment:持卡人姓名;size:20;"`                                       //持卡人姓名
	TaxCode        *string `json:"taxCode" form:"taxCode" gorm:"column:tax_code;comment:税务登记号;size:50;"`                                          //税务登记号
	Sort           *int    `json:"sort" form:"sort" gorm:"column:sort;comment:排序;size:10;"`                                                          //排序
	CreatedBy      uint    `gorm:"column:created_by;comment:创建者"`
	UpdatedBy      uint    `gorm:"column:updated_by;comment:更新者"`
	DeletedBy      uint    `gorm:"column:deleted_by;comment:删除者"`
}

// TableName lsSupplier表 LsSupplier自定义表名 ls_supplier
func (LsSupplier) TableName() string {
	return "ls_supplier"
}

type LsArea struct {
	ID       string   `gorm:"primaryKey;type:varchar(255);not null;comment:中国地区表"`
	Name     string   `gorm:"type:varchar(50);default:null;comment:地区名称"`
	Level    string   `gorm:"type:varchar(50);default:null;comment:省市县街道(province>city>district>street)"`
	LevelNum int      `gorm:"type:int;default:null;comment:省市县街道级别号(0>1>2>3)"`
	Adcode   string   `gorm:"type:varchar(255);default:null;comment:编码"`
	Pid      string   `gorm:"type:varchar(255);default:null;comment:父id"`
	Padcode  string   `gorm:"type:varchar(255);default:null;comment:父编码"`
	Children []LsArea `json:"children" gorm:"-"`
}

func (LsArea) TableName() string {
	return "ls_area"
}
