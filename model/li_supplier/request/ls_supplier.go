
package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"time"
)

type LsSupplierSearch struct{
    StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
    EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
    SupplierName  *string `json:"supplierName" form:"supplierName" `
    SupplierUser  *string `json:"supplierUser" form:"supplierUser" `
    SupplierMobile  *string `json:"supplierMobile" form:"supplierMobile" `
    SupplierTel  *string `json:"supplierTel" form:"supplierTel" `
    SupplierEmail  *string `json:"supplierEmail" form:"supplierEmail" `
    BankAccount  *string `json:"bankAccount" form:"bankAccount" `
    request.PageInfo
}
