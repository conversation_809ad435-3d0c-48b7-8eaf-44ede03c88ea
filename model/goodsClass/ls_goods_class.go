// 自动生成模板LsGoodsClass
package goodsClass

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 商品分类 结构体  LsGoodsClass
type LsGoodsClass struct {
	global.GVA_MODEL
	ClassName *string        `json:"className" form:"className" gorm:"index;column:class_name;comment:分类名称;size:20;" binding:"required"` //分类名称
	Pid       *int           `json:"pid" form:"pid" gorm:"column:pid;comment:0 代表顶级;size:10;" binding:"required"`                        //父级id
	ClassIcon *string        `json:"classIcon" form:"classIcon" gorm:"column:class_icon;comment:分类图标;size:100;"`                         //分类图标
	Sort      *int           `json:"sort" form:"sort" gorm:"column:sort;comment:排序;size:10;"`                                            //排序
	Enable    *bool          `json:"enable" form:"enable" gorm:"column:enable;comment:0 不展示 1展示;"`                                       //0 不展示 1展示
	CreatedBy uint           `gorm:"column:created_by;comment:创建者"`
	UpdatedBy uint           `gorm:"column:updated_by;comment:更新者"`
	DeletedBy uint           `gorm:"column:deleted_by;comment:删除者"`
	Children  []LsGoodsClass `json:"children" gorm:"-"`
}

// TableName 商品分类 LsGoodsClass自定义表名 ls_goods_class
func (LsGoodsClass) TableName() string {
	return "ls_goods_class"
}
