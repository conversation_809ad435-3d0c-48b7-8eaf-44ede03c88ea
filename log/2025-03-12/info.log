[github.com/flipped-aurora/gin-vue-admin/server]2025-03-12 15:05:54.568	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-12 15:05:54.785	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-12 15:05:55.063	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-12 15:05:55.119	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-12 15:05:56.379	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-12 15:05:56.381	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
