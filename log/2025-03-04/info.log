[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:26:54.455	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:26:54.497	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/plugin.go:10	项目暂未初始化，无法安装插件，初始化后重启项目即可完成插件安装
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:26:54.500	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:26:54.503	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:27:10.579	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/sys_initdb.go:57	前往初始化数据库
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:33:48.995	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:33:49.054	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:33:49.731	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:33:49.734	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:59:21.100	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:59:21.135	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:59:21.932	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:59:21.933	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 12:30:14.974	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 12:30:15.071	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 12:30:18.558	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 12:30:18.559	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 15:56:41.457	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 15:56:41.549	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 15:56:48.585	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 15:56:48.609	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 19:52:37.227	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 19:52:37.278	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 19:52:39.102	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 19:52:39.103	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 20:39:18.113	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 20:39:18.204	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 20:39:21.394	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 20:39:21.394	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
