[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:29:47.292	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:77	获取失败!	{"error": "读取service文件夹失败!: open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.", "errorVerbose": "open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.\n读取service文件夹失败!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodePackage).All\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_package.go:127\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodePackageApi).All\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:75\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:30:51.294	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:77	获取失败!	{"error": "读取service文件夹失败!: open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.", "errorVerbose": "open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.\n读取service文件夹失败!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodePackage).All\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_package.go:127\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodePackageApi).All\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:75\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:30:57.196	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:77	获取失败!	{"error": "读取service文件夹失败!: open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.", "errorVerbose": "open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.\n读取service文件夹失败!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodePackage).All\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_package.go:127\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodePackageApi).All\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:75\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:31:12.029	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:77	获取失败!	{"error": "读取service文件夹失败!: open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.", "errorVerbose": "open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.\n读取service文件夹失败!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodePackage).All\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_package.go:127\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodePackageApi).All\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:75\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:32:06.331	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:77	获取失败!	{"error": "读取service文件夹失败!: open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.", "errorVerbose": "open C:\\GoWork\\src\\ZG5\\server\\service: The system cannot find the path specified.\n读取service文件夹失败!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodePackage).All\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_package.go:127\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodePackageApi).All\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_package.go:75\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:51:35.856	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/sys_authority.go:166	获取失败!	{"error": "invalid connection"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-04 10:55:50.113	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:66	register table failed	{"error": "dial tcp **************:3306: connectex: A socket operation was attempted to an unreachable network."}
