[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 10:02:50.687	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_template.go:78	创建失败!	{"error": "已经创建过此数据结构,请勿重复创建!", "errorVerbose": "已经创建过此数据结构,请勿重复创建!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodeTemplate).Create\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_template.go:70\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodeTemplateApi).Create\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_template.go:76\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 10:02:55.659	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_template.go:78	创建失败!	{"error": "已经创建过此数据结构,请勿重复创建!", "errorVerbose": "已经创建过此数据结构,请勿重复创建!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodeTemplate).Create\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_template.go:70\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodeTemplateApi).Create\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_template.go:76\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 10:02:55.940	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_template.go:78	创建失败!	{"error": "已经创建过此数据结构,请勿重复创建!", "errorVerbose": "已经创建过此数据结构,请勿重复创建!\ngithub.com/flipped-aurora/gin-vue-admin/server/service/system.(*autoCodeTemplate).Create\n\tC:/GoWork/src/ZG5/like_shop_admin/service/system/auto_code_template.go:70\ngithub.com/flipped-aurora/gin-vue-admin/server/api/v1/system.(*AutoCodeTemplateApi).Create\n\tC:/GoWork/src/ZG5/like_shop_admin/api/v1/system/auto_code_template.go:76\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.CasbinHandler.func3\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/casbin_rbac.go:34\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/flipped-aurora/gin-vue-admin/server/initialize.Routers.JWTAuth.func2\n\tC:/GoWork/src/ZG5/like_shop_admin/middleware/jwt.go:72\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/GoWork/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Program Files/Go1.24.0/src/net/http/server.go:2102\nruntime.goexit\n\tC:/Program Files/Go1.24.0/src/runtime/asm_amd64.s:1700"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:05:42.418	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/ls_goods/ls_goods.go:37	创建失败!	{"error": "Error 1406 (22001): Data too long for column 'video_cover' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 21:11:33.103	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/ls_goods/ls_goods.go:37	创建失败!	{"error": "Error 1364 (HY000): Field 'good_code' doesn't have a default value"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 21:15:33.221	[31merror[0m	C:/GoWork/src/ZG5/like_shop_admin/api/v1/ls_goods/ls_goods.go:37	创建失败!	{"error": "Error 1364 (HY000): Field 'good_code' doesn't have a default value"}
