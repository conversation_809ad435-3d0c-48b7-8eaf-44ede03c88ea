[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 09:59:42.020	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 09:59:42.146	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 09:59:42.270	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 09:59:42.311	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 09:59:43.057	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 09:59:43.059	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:09:24.800	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:09:24.900	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:09:25.053	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:09:25.081	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:09:25.638	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:09:25.639	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:49:01.214	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:49:01.327	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:49:01.443	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:49:01.467	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:49:02.083	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-10 20:49:02.084	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
