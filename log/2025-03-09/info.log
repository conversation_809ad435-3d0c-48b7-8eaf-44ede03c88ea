[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 10:54:06.716	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 10:54:06.947	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 10:54:07.129	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 10:54:07.193	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 10:54:08.834	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 10:54:08.834	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:08:53.900	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:08:54.102	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:08:54.336	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:08:54.456	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:08:55.909	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:08:55.911	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:33:36.941	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:33:37.170	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:33:37.423	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:33:37.492	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:33:39.191	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:33:39.192	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:40:25.131	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:40:25.383	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:40:25.678	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:40:25.741	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:40:27.318	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:40:27.319	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:42:40.255	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:42:40.475	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:42:40.707	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:42:40.756	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:42:42.414	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:42:42.415	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:47:55.394	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:47:55.618	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:47:55.822	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:47:55.884	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:47:57.694	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 11:47:57.695	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:43:58.677	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:43:58.777	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:43:58.891	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:43:58.914	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:43:59.465	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:43:59.466	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:55:33.307	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:55:33.429	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:55:33.523	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:55:33.557	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:55:34.270	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:55:34.271	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:58:48.717	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:58:48.995	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:58:49.192	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:58:49.232	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:58:49.886	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 15:58:49.888	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 16:08:09.762	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 16:08:09.869	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 16:08:09.990	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 16:08:10.028	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 16:08:10.676	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 16:08:10.677	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:06:09.790	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:06:09.931	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:06:10.035	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:06:10.088	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:06:10.769	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:06:10.771	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:39:15.769	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:39:15.980	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:39:16.142	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:39:16.194	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:39:17.444	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 19:39:17.445	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 21:01:08.762	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 21:01:09.907	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 21:01:11.010	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 21:01:11.052	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 21:01:11.686	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-09 21:01:11.686	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
