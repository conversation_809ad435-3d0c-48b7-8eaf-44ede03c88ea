[github.com/flipped-aurora/gin-vue-admin/server]2025-03-08 09:41:41.698	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-08 09:41:43.199	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-08 09:41:43.802	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/redis.go:35	redis connect ping response:	{"name": "", "pong": "PONG"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-08 09:41:44.262	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-08 09:41:48.370	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/initialize/router.go:108	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-03-08 09:41:48.373	[34minfo[0m	C:/GoWork/src/ZG5/like_shop_admin/core/server.go:38	server run success on 	{"address": ":8888"}
