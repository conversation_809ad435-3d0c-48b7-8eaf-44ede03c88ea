package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/goodsClass"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/li_supplier"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/ls_brand"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/ls_goods"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
)

var ApiGroupApp = new(ApiGroup)

type ApiGroup struct {
	SystemApiGroup      system.ApiGroup
	ExampleApiGroup     example.ApiGroup
	Ls_goodsApiGroup    ls_goods.ApiGroup
	Ls_brandApiGroup    ls_brand.ApiGroup
	GoodsClassApiGroup  goodsClass.ApiGroup
	Li_supplierApiGroup li_supplier.ApiGroup
}
