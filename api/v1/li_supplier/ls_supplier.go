package li_supplier

import (
	"encoding/json"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/li_supplier"
	li_supplierReq "github.com/flipped-aurora/gin-vue-admin/server/model/li_supplier/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"strings"
)

type LsSupplierApi struct{}

// CreateLsSupplier 创建lsSupplier表
// @Tags LsSupplier
// @Summary 创建lsSupplier表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body li_supplier.LsSupplier true "创建lsSupplier表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /lsSupplier/createLsSupplier [post]
func (lsSupplierApi *LsSupplierApi) CreateLsSupplier(c *gin.Context) {
	var lsSupplier li_supplier.LsSupplier
	var area li_supplier.LsArea
	var areas li_supplier.LsArea
	var areaList li_supplier.LsArea

	err := c.ShouldBindJSON(&lsSupplier)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsSupplier.CreatedBy = utils.GetUserID(c)
	s := strings.Split(lsSupplier.Area, "/")
	lsSupplier.SupplierCode = uuid.NewString()
	if s[0] != "" {
		global.GVA_DB.Where("adcode=?", s[0]).First(&area)
		lsSupplier.Province = area.Name
	}
	if s[1] != "" {
		global.GVA_DB.Where("adcode=?", s[1]).First(&areas)
		lsSupplier.City = areas.Name
	}
	if s[2] != "" {
		global.GVA_DB.Where("adcode=?", s[2]).First(&areaList)
		lsSupplier.Area = areaList.Name
	}
	err = lsSupplierService.CreateLsSupplier(&lsSupplier)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteLsSupplier 删除lsSupplier表
// @Tags LsSupplier
// @Summary 删除lsSupplier表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body li_supplier.LsSupplier true "删除lsSupplier表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /lsSupplier/deleteLsSupplier [delete]
func (lsSupplierApi *LsSupplierApi) DeleteLsSupplier(c *gin.Context) {
	ID := c.Query("ID")
	userID := utils.GetUserID(c)
	err := lsSupplierService.DeleteLsSupplier(ID, userID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteLsSupplierByIds 批量删除lsSupplier表
// @Tags LsSupplier
// @Summary 批量删除lsSupplier表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /lsSupplier/deleteLsSupplierByIds [delete]
func (lsSupplierApi *LsSupplierApi) DeleteLsSupplierByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	userID := utils.GetUserID(c)
	err := lsSupplierService.DeleteLsSupplierByIds(IDs, userID)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateLsSupplier 更新lsSupplier表
// @Tags LsSupplier
// @Summary 更新lsSupplier表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body li_supplier.LsSupplier true "更新lsSupplier表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /lsSupplier/updateLsSupplier [put]
func (lsSupplierApi *LsSupplierApi) UpdateLsSupplier(c *gin.Context) {
	var lsSupplier li_supplier.LsSupplier
	err := c.ShouldBindJSON(&lsSupplier)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsSupplier.UpdatedBy = utils.GetUserID(c)
	err = lsSupplierService.UpdateLsSupplier(lsSupplier)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindLsSupplier 用id查询lsSupplier表
// @Tags LsSupplier
// @Summary 用id查询lsSupplier表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询lsSupplier表"
// @Success 200 {object} response.Response{data=li_supplier.LsSupplier,msg=string} "查询成功"
// @Router /lsSupplier/findLsSupplier [get]
func (lsSupplierApi *LsSupplierApi) FindLsSupplier(c *gin.Context) {
	ID := c.Query("ID")
	relsSupplier, err := lsSupplierService.GetLsSupplier(ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(relsSupplier, c)
}

// GetLsSupplierList 分页获取lsSupplier表列表
// @Tags LsSupplier
// @Summary 分页获取lsSupplier表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query li_supplierReq.LsSupplierSearch true "分页获取lsSupplier表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /lsSupplier/getLsSupplierList [get]
func (lsSupplierApi *LsSupplierApi) GetLsSupplierList(c *gin.Context) {
	var pageInfo li_supplierReq.LsSupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := lsSupplierService.GetLsSupplierInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetLsSupplierPublic 不需要鉴权的lsSupplier表接口
// @Tags LsSupplier
// @Summary 不需要鉴权的lsSupplier表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /lsSupplier/getLsSupplierPublic [get]
func (lsSupplierApi *LsSupplierApi) GetLsSupplierPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	lsSupplierService.GetLsSupplierPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的lsSupplier表接口信息",
	}, "获取成功", c)
}

func (lsSupplierApi *LsSupplierApi) GetAllLsAreaList(c *gin.Context) {
	var pageInfo li_supplierReq.LsSupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	var areas interface{}
	area := global.GVA_REDIS.Get(c, "area").Val()
	if area != "" {
		err = json.Unmarshal([]byte(area), &areas)
		if err != nil {
			panic(err)
		}
		response.OkWithDetailed(areas, "获取成功", c)
	}
	if area == "" {
		list := lsSupplierService.GetAllLsArea("0", c)
		marshal, _ := json.Marshal(list)
		global.GVA_REDIS.Set(c, "area", marshal, 0)
		response.OkWithDetailed(list, "获取成功", c)
	}

}

func (lsSupplierApi *LsSupplierApi) GetAllLsSupplierList(c *gin.Context) {
	supplier, err := lsSupplierService.GetAllSuppler()
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(supplier, "获取成功", c)
}
