package ls_brand

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ls_brand"
	ls_brandReq "github.com/flipped-aurora/gin-vue-admin/server/model/ls_brand/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LsBrandApi struct{}

// CreateLsBrand 创建lsBrand表
// @Tags LsBrand
// @Summary 创建lsBrand表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ls_brand.LsBrand true "创建lsBrand表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /lsBrand/createLsBrand [post]
func (lsBrandApi *LsBrandApi) CreateLsBrand(c *gin.Context) {
	var lsBrand ls_brand.LsBrand
	err := c.ShouldBindJSON(&lsBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsBrand.CreatedBy = utils.GetUserID(c)
	err = lsBrandService.CreateLsBrand(&lsBrand)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteLsBrand 删除lsBrand表
// @Tags LsBrand
// @Summary 删除lsBrand表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ls_brand.LsBrand true "删除lsBrand表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /lsBrand/deleteLsBrand [delete]
func (lsBrandApi *LsBrandApi) DeleteLsBrand(c *gin.Context) {
	ID := c.Query("ID")
	userID := utils.GetUserID(c)
	err := lsBrandService.DeleteLsBrand(ID, userID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteLsBrandByIds 批量删除lsBrand表
// @Tags LsBrand
// @Summary 批量删除lsBrand表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /lsBrand/deleteLsBrandByIds [delete]
func (lsBrandApi *LsBrandApi) DeleteLsBrandByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	userID := utils.GetUserID(c)
	err := lsBrandService.DeleteLsBrandByIds(IDs, userID)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateLsBrand 更新lsBrand表
// @Tags LsBrand
// @Summary 更新lsBrand表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ls_brand.LsBrand true "更新lsBrand表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /lsBrand/updateLsBrand [put]
func (lsBrandApi *LsBrandApi) UpdateLsBrand(c *gin.Context) {
	var lsBrand ls_brand.LsBrand
	err := c.ShouldBindJSON(&lsBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsBrand.UpdatedBy = utils.GetUserID(c)
	err = lsBrandService.UpdateLsBrand(lsBrand)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindLsBrand 用id查询lsBrand表
// @Tags LsBrand
// @Summary 用id查询lsBrand表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询lsBrand表"
// @Success 200 {object} response.Response{data=ls_brand.LsBrand,msg=string} "查询成功"
// @Router /lsBrand/findLsBrand [get]
func (lsBrandApi *LsBrandApi) FindLsBrand(c *gin.Context) {
	ID := c.Query("ID")
	relsBrand, err := lsBrandService.GetLsBrand(ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(relsBrand, c)
}

// GetLsBrandList 分页获取lsBrand表列表
// @Tags LsBrand
// @Summary 分页获取lsBrand表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query ls_brandReq.LsBrandSearch true "分页获取lsBrand表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /lsBrand/getLsBrandList [get]
func (lsBrandApi *LsBrandApi) GetLsBrandList(c *gin.Context) {
	var pageInfo ls_brandReq.LsBrandSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := lsBrandService.GetLsBrandInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetLsBrandPublic 不需要鉴权的lsBrand表接口
// @Tags LsBrand
// @Summary 不需要鉴权的lsBrand表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /lsBrand/getLsBrandPublic [get]
func (lsBrandApi *LsBrandApi) GetLsBrandPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	lsBrandService.GetLsBrandPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的lsBrand表接口信息",
	}, "获取成功", c)
}

//获取所有品牌列表

func (lsBrandApi *LsBrandApi) GetAllLsBrandList(c *gin.Context) {
	var pageInfo ls_brandReq.LsBrandSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := lsBrandService.GetAllLsBrandInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(list, "获取成功", c)
}
