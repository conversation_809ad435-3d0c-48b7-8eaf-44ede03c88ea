package ls_goods

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ls_goods"
	ls_goodsReq "github.com/flipped-aurora/gin-vue-admin/server/model/ls_goods/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LsGoodsApi struct{}

// CreateLsGoods 创建lsGoods表
// @Tags LsGoods
// @Summary 创建lsGoods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ls_goods.LsGoods true "创建lsGoods表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /lsGoods/createLsGoods [post]
func (lsGoodsApi *LsGoodsApi) CreateLsGoods(c *gin.Context) {
	//var lsGood ls_goods.LsGoods

	var lsGoods ls_goodsReq.Goods
	err := c.ShouldBindJSON(&lsGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsGoods.CreatedBy = utils.GetUserID(c)
	err = lsGoodsService.CreateLsGoods(&lsGoods)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteLsGoods 删除lsGoods表
// @Tags LsGoods
// @Summary 删除lsGoods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ls_goods.LsGoods true "删除lsGoods表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /lsGoods/deleteLsGoods [delete]
func (lsGoodsApi *LsGoodsApi) DeleteLsGoods(c *gin.Context) {
	ID := c.Query("ID")
	userID := utils.GetUserID(c)
	err := lsGoodsService.DeleteLsGoods(ID, userID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteLsGoodsByIds 批量删除lsGoods表
// @Tags LsGoods
// @Summary 批量删除lsGoods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /lsGoods/deleteLsGoodsByIds [delete]
func (lsGoodsApi *LsGoodsApi) DeleteLsGoodsByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	userID := utils.GetUserID(c)
	err := lsGoodsService.DeleteLsGoodsByIds(IDs, userID)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateLsGoods 更新lsGoods表
// @Tags LsGoods
// @Summary 更新lsGoods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ls_goods.LsGoods true "更新lsGoods表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /lsGoods/updateLsGoods [put]
func (lsGoodsApi *LsGoodsApi) UpdateLsGoods(c *gin.Context) {
	var lsGoods ls_goods.LsGoods
	err := c.ShouldBindJSON(&lsGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsGoods.UpdatedBy = utils.GetUserID(c)
	err = lsGoodsService.UpdateLsGoods(lsGoods)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindLsGoods 用id查询lsGoods表
// @Tags LsGoods
// @Summary 用id查询lsGoods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询lsGoods表"
// @Success 200 {object} response.Response{data=ls_goods.LsGoods,msg=string} "查询成功"
// @Router /lsGoods/findLsGoods [get]
func (lsGoodsApi *LsGoodsApi) FindLsGoods(c *gin.Context) {
	ID := c.Query("ID")
	relsGoods, err := lsGoodsService.GetLsGoods(ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(relsGoods, c)
}

// GetLsGoodsList 分页获取lsGoods表列表
// @Tags LsGoods
// @Summary 分页获取lsGoods表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query ls_goodsReq.LsGoodsSearch true "分页获取lsGoods表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /lsGoods/getLsGoodsList [get]
func (lsGoodsApi *LsGoodsApi) GetLsGoodsList(c *gin.Context) {
	var pageInfo ls_goodsReq.LsGoodsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := lsGoodsService.GetLsGoodsInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetLsGoodsPublic 不需要鉴权的lsGoods表接口
// @Tags LsGoods
// @Summary 不需要鉴权的lsGoods表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /lsGoods/getLsGoodsPublic [get]
func (lsGoodsApi *LsGoodsApi) GetLsGoodsPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	lsGoodsService.GetLsGoodsPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的lsGoods表接口信息",
	}, "获取成功", c)
}
