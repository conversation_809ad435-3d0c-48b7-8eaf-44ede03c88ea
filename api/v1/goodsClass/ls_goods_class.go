package goodsClass

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/goodsClass"
	goodsClassReq "github.com/flipped-aurora/gin-vue-admin/server/model/goodsClass/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LsGoodsClassApi struct{}

// CreateLsGoodsClass 创建商品分类
// @Tags LsGoodsClass
// @Summary 创建商品分类
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body goodsClass.LsGoodsClass true "创建商品分类"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /lsGoodsClass/createLsGoodsClass [post]
func (lsGoodsClassApi *LsGoodsClassApi) CreateLsGoodsClass(c *gin.Context) {
	var lsGoodsClass goodsClass.LsGoodsClass
	err := c.ShouldBindJSON(&lsGoodsClass)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsGoodsClass.CreatedBy = utils.GetUserID(c)
	err = lsGoodsClassService.CreateLsGoodsClass(&lsGoodsClass)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteLsGoodsClass 删除商品分类
// @Tags LsGoodsClass
// @Summary 删除商品分类
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body goodsClass.LsGoodsClass true "删除商品分类"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /lsGoodsClass/deleteLsGoodsClass [delete]
func (lsGoodsClassApi *LsGoodsClassApi) DeleteLsGoodsClass(c *gin.Context) {
	ID := c.Query("ID")
	userID := utils.GetUserID(c)
	err := lsGoodsClassService.DeleteLsGoodsClass(ID, userID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteLsGoodsClassByIds 批量删除商品分类
// @Tags LsGoodsClass
// @Summary 批量删除商品分类
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /lsGoodsClass/deleteLsGoodsClassByIds [delete]
func (lsGoodsClassApi *LsGoodsClassApi) DeleteLsGoodsClassByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	userID := utils.GetUserID(c)
	err := lsGoodsClassService.DeleteLsGoodsClassByIds(IDs, userID)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateLsGoodsClass 更新商品分类
// @Tags LsGoodsClass
// @Summary 更新商品分类
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body goodsClass.LsGoodsClass true "更新商品分类"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /lsGoodsClass/updateLsGoodsClass [put]
func (lsGoodsClassApi *LsGoodsClassApi) UpdateLsGoodsClass(c *gin.Context) {
	var lsGoodsClass goodsClass.LsGoodsClass
	err := c.ShouldBindJSON(&lsGoodsClass)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	lsGoodsClass.UpdatedBy = utils.GetUserID(c)
	err = lsGoodsClassService.UpdateLsGoodsClass(lsGoodsClass)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindLsGoodsClass 用id查询商品分类
// @Tags LsGoodsClass
// @Summary 用id查询商品分类
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询商品分类"
// @Success 200 {object} response.Response{data=goodsClass.LsGoodsClass,msg=string} "查询成功"
// @Router /lsGoodsClass/findLsGoodsClass [get]
func (lsGoodsClassApi *LsGoodsClassApi) FindLsGoodsClass(c *gin.Context) {
	ID := c.Query("ID")
	relsGoodsClass, err := lsGoodsClassService.GetLsGoodsClass(ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(relsGoodsClass, c)
}

// GetLsGoodsClassList 分页获取商品分类列表
// @Tags LsGoodsClass
// @Summary 分页获取商品分类列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query goodsClassReq.LsGoodsClassSearch true "分页获取商品分类列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /lsGoodsClass/getLsGoodsClassList [get]
func (lsGoodsClassApi *LsGoodsClassApi) GetLsGoodsClassList(c *gin.Context) {
	var pageInfo goodsClassReq.LsGoodsClassSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := lsGoodsClassService.GetLsGoodsClassInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetLsGoodsClassPublic 不需要鉴权的商品分类接口
// @Tags LsGoodsClass
// @Summary 不需要鉴权的商品分类接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /lsGoodsClass/getLsGoodsClassPublic [get]
func (lsGoodsClassApi *LsGoodsClassApi) GetLsGoodsClassPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	lsGoodsClassService.GetLsGoodsClassPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的商品分类接口信息",
	}, "获取成功", c)
}
