package li_supplier

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LsSupplierRouter struct{}

// InitLsSupplierRouter 初始化 lsSupplier表 路由信息
func (s *LsSupplierRouter) InitLsSupplierRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	lsSupplierRouter := Router.Group("lsSupplier").Use(middleware.OperationRecord())
	lsSupplierRouterWithoutRecord := Router.Group("lsSupplier")
	lsSupplierRouterWithoutAuth := PublicRouter.Group("lsSupplier")
	{
		lsSupplierRouter.POST("createLsSupplier", lsSupplierApi.CreateLsSupplier)             // 新建lsSupplier表
		lsSupplierRouter.DELETE("deleteLsSupplier", lsSupplierApi.DeleteLsSupplier)           // 删除lsSupplier表
		lsSupplierRouter.DELETE("deleteLsSupplierByIds", lsSupplierApi.DeleteLsSupplierByIds) // 批量删除lsSupplier表
		lsSupplierRouter.PUT("updateLsSupplier", lsSupplierApi.UpdateLsSupplier)              // 更新lsSupplier表
	}
	{
		lsSupplierRouterWithoutRecord.GET("findLsSupplier", lsSupplierApi.FindLsSupplier)       // 根据ID获取lsSupplier表
		lsSupplierRouterWithoutRecord.GET("getLsSupplierList", lsSupplierApi.GetLsSupplierList) // 获取lsSupplier表列表
	}
	{
		lsSupplierRouterWithoutAuth.GET("getLsSupplierPublic", lsSupplierApi.GetLsSupplierPublic)   // lsSupplier表开放接口
		lsSupplierRouterWithoutAuth.GET("getAllLsAreaList", lsSupplierApi.GetAllLsAreaList)         // 获取地区列表
		lsSupplierRouterWithoutAuth.GET("getAllLsSupplierList", lsSupplierApi.GetAllLsSupplierList) // 获取lsSupplier表列表

	}
}
