package ls_brand

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LsBrandRouter struct{}

// InitLsBrandRouter 初始化 lsBrand表 路由信息
func (s *LsBrandRouter) InitLsBrandRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	lsBrandRouter := Router.Group("lsBrand").Use(middleware.OperationRecord())
	lsBrandRouterWithoutRecord := Router.Group("lsBrand")
	lsBrandRouterWithoutAuth := PublicRouter.Group("lsBrand")
	{
		lsBrandRouter.POST("createLsBrand", lsBrandApi.CreateLsBrand)             // 新建lsBrand表
		lsBrandRouter.DELETE("deleteLsBrand", lsBrandApi.DeleteLsBrand)           // 删除lsBrand表
		lsBrandRouter.DELETE("deleteLsBrandByIds", lsBrandApi.DeleteLsBrandByIds) // 批量删除lsBrand表
		lsBrandRouter.PUT("updateLsBrand", lsBrandApi.UpdateLsBrand)              // 更新lsBrand表
	}
	{
		lsBrandRouterWithoutRecord.GET("findLsBrand", lsBrandApi.FindLsBrand)       // 根据ID获取lsBrand表
		lsBrandRouterWithoutRecord.GET("getLsBrandList", lsBrandApi.GetLsBrandList) // 获取lsBrand表列表
	}
	{
		lsBrandRouterWithoutAuth.GET("getLsBrandPublic", lsBrandApi.GetLsBrandPublic)   // lsBrand表开放接口
		lsBrandRouterWithoutAuth.GET("getAllLsBrandList", lsBrandApi.GetAllLsBrandList) //获取所有品牌列表无分页
	}
}
