package ls_goods

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LsGoodsRouter struct {}

// InitLsGoodsRouter 初始化 lsGoods表 路由信息
func (s *LsGoodsRouter) InitLsGoodsRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	lsGoodsRouter := Router.Group("lsGoods").Use(middleware.OperationRecord())
	lsGoodsRouterWithoutRecord := Router.Group("lsGoods")
	lsGoodsRouterWithoutAuth := PublicRouter.Group("lsGoods")
	{
		lsGoodsRouter.POST("createLsGoods", lsGoodsApi.CreateLsGoods)   // 新建lsGoods表
		lsGoodsRouter.DELETE("deleteLsGoods", lsGoodsApi.DeleteLsGoods) // 删除lsGoods表
		lsGoodsRouter.DELETE("deleteLsGoodsByIds", lsGoodsApi.DeleteLsGoodsByIds) // 批量删除lsGoods表
		lsGoodsRouter.PUT("updateLsGoods", lsGoodsApi.UpdateLsGoods)    // 更新lsGoods表
	}
	{
		lsGoodsRouterWithoutRecord.GET("findLsGoods", lsGoodsApi.FindLsGoods)        // 根据ID获取lsGoods表
		lsGoodsRouterWithoutRecord.GET("getLsGoodsList", lsGoodsApi.GetLsGoodsList)  // 获取lsGoods表列表
	}
	{
	    lsGoodsRouterWithoutAuth.GET("getLsGoodsPublic", lsGoodsApi.GetLsGoodsPublic)  // lsGoods表开放接口
	}
}
