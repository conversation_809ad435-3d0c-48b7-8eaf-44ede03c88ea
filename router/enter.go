package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router/example"
	"github.com/flipped-aurora/gin-vue-admin/server/router/goodsClass"
	"github.com/flipped-aurora/gin-vue-admin/server/router/li_supplier"
	"github.com/flipped-aurora/gin-vue-admin/server/router/ls_brand"
	"github.com/flipped-aurora/gin-vue-admin/server/router/ls_goods"
	"github.com/flipped-aurora/gin-vue-admin/server/router/system"
)

var RouterGroupApp = new(RouterGroup)

type RouterGroup struct {
	System      system.RouterGroup
	Example     example.RouterGroup
	Ls_goods    ls_goods.RouterGroup
	Ls_brand    ls_brand.RouterGroup
	GoodsClass  goodsClass.RouterGroup
	Li_supplier li_supplier.RouterGroup
}
