package goodsClass

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LsGoodsClassRouter struct {}

// InitLsGoodsClassRouter 初始化 商品分类 路由信息
func (s *LsGoodsClassRouter) InitLsGoodsClassRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	lsGoodsClassRouter := Router.Group("lsGoodsClass").Use(middleware.OperationRecord())
	lsGoodsClassRouterWithoutRecord := Router.Group("lsGoodsClass")
	lsGoodsClassRouterWithoutAuth := PublicRouter.Group("lsGoodsClass")
	{
		lsGoodsClassRouter.POST("createLsGoodsClass", lsGoodsClassApi.CreateLsGoodsClass)   // 新建商品分类
		lsGoodsClassRouter.DELETE("deleteLsGoodsClass", lsGoodsClassApi.DeleteLsGoodsClass) // 删除商品分类
		lsGoodsClassRouter.DELETE("deleteLsGoodsClassByIds", lsGoodsClassApi.DeleteLsGoodsClassByIds) // 批量删除商品分类
		lsGoodsClassRouter.PUT("updateLsGoodsClass", lsGoodsClassApi.UpdateLsGoodsClass)    // 更新商品分类
	}
	{
		lsGoodsClassRouterWithoutRecord.GET("findLsGoodsClass", lsGoodsClassApi.FindLsGoodsClass)        // 根据ID获取商品分类
		lsGoodsClassRouterWithoutRecord.GET("getLsGoodsClassList", lsGoodsClassApi.GetLsGoodsClassList)  // 获取商品分类列表
	}
	{
	    lsGoodsClassRouterWithoutAuth.GET("getLsGoodsClassPublic", lsGoodsClassApi.GetLsGoodsClassPublic)  // 商品分类开放接口
	}
}
