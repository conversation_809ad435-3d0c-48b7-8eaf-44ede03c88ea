package li_supplier

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/li_supplier"
	li_supplierReq "github.com/flipped-aurora/gin-vue-admin/server/model/li_supplier/request"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type LsSupplierService struct{}

// CreateLsSupplier 创建lsSupplier表记录
// Author [yourname](https://github.com/yourname)
func (lsSupplierService *LsSupplierService) CreateLsSupplier(lsSupplier *li_supplier.LsSupplier) (err error) {

	err = global.GVA_DB.Create(lsSupplier).Error
	return err
}

// DeleteLsSupplier 删除lsSupplier表记录
// Author [yourname](https://github.com/yourname)
func (lsSupplierService *LsSupplierService) DeleteLsSupplier(ID string, userID uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&li_supplier.LsSupplier{}).Where("id = ?", ID).Update("deleted_by", userID).Error; err != nil {
			return err
		}
		if err = tx.Delete(&li_supplier.LsSupplier{}, "id = ?", ID).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// DeleteLsSupplierByIds 批量删除lsSupplier表记录
// Author [yourname](https://github.com/yourname)
func (lsSupplierService *LsSupplierService) DeleteLsSupplierByIds(IDs []string, deleted_by uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&li_supplier.LsSupplier{}).Where("id in ?", IDs).Update("deleted_by", deleted_by).Error; err != nil {
			return err
		}
		if err := tx.Where("id in ?", IDs).Delete(&li_supplier.LsSupplier{}).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateLsSupplier 更新lsSupplier表记录
// Author [yourname](https://github.com/yourname)
func (lsSupplierService *LsSupplierService) UpdateLsSupplier(lsSupplier li_supplier.LsSupplier) (err error) {
	err = global.GVA_DB.Model(&li_supplier.LsSupplier{}).Where("id = ?", lsSupplier.ID).Updates(&lsSupplier).Error
	return err
}

// GetLsSupplier 根据ID获取lsSupplier表记录
// Author [yourname](https://github.com/yourname)
func (lsSupplierService *LsSupplierService) GetLsSupplier(ID string) (lsSupplier li_supplier.LsSupplier, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&lsSupplier).Error
	return
}

// GetLsSupplierInfoList 分页获取lsSupplier表记录
// Author [yourname](https://github.com/yourname)
func (lsSupplierService *LsSupplierService) GetLsSupplierInfoList(info li_supplierReq.LsSupplierSearch) (list []li_supplier.LsSupplier, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&li_supplier.LsSupplier{})
	var lsSuppliers []li_supplier.LsSupplier
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.SupplierName != nil && *info.SupplierName != "" {
		db = db.Where("supplier_name = ?", *info.SupplierName)
	}
	if info.SupplierUser != nil && *info.SupplierUser != "" {
		db = db.Where("supplier_user LIKE ?", "%"+*info.SupplierUser+"%")
	}
	if info.SupplierMobile != nil && *info.SupplierMobile != "" {
		db = db.Where("supplier_mobile = ?", *info.SupplierMobile)
	}
	if info.SupplierTel != nil && *info.SupplierTel != "" {
		db = db.Where("supplier_tel = ?", *info.SupplierTel)
	}
	if info.SupplierEmail != nil && *info.SupplierEmail != "" {
		db = db.Where("supplier_email = ?", *info.SupplierEmail)
	}
	if info.BankAccount != nil && *info.BankAccount != "" {
		db = db.Where("bank_account = ?", *info.BankAccount)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&lsSuppliers).Error
	return lsSuppliers, total, err
}
func (lsSupplierService *LsSupplierService) GetLsSupplierPublic() {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

func (lsSupplierService *LsSupplierService) GetAllLsArea(id string, c *gin.Context) []li_supplier.LsArea {
	var area []li_supplier.LsArea

	err := global.GVA_DB.Where(" pid=? ", id).Find(&area).Error
	if err != nil {
		panic(err)
	}
	list := GetAllLsAreaList(area)
	return list
}

func GetAllLsAreaList(area []li_supplier.LsArea) []li_supplier.LsArea {
	var list []li_supplier.LsArea
	var areas []li_supplier.LsArea
	for _, a := range area {
		global.GVA_DB.Where("pid=?", a.ID).Find(&areas)
		list = append(list, li_supplier.LsArea{
			ID:       a.ID,
			Name:     a.Name,
			Level:    a.Level,
			LevelNum: a.LevelNum,
			Adcode:   a.Adcode,
			Pid:      a.Pid,
			Padcode:  a.Padcode,
			Children: GetAllLsAreaList(areas),
		})
	}
	return list
}
func (lsSupplierService *LsSupplierService) GetAllSuppler() ([]li_supplier.LsSupplier, error) {
	var list []li_supplier.LsSupplier

	err := global.GVA_DB.Find(&list).Error
	return list, err
}
