package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/example"
	"github.com/flipped-aurora/gin-vue-admin/server/service/goodsClass"
	"github.com/flipped-aurora/gin-vue-admin/server/service/li_supplier"
	"github.com/flipped-aurora/gin-vue-admin/server/service/ls_brand"
	"github.com/flipped-aurora/gin-vue-admin/server/service/ls_goods"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
)

var ServiceGroupApp = new(ServiceGroup)

type ServiceGroup struct {
	SystemServiceGroup      system.ServiceGroup
	ExampleServiceGroup     example.ServiceGroup
	Ls_goodsServiceGroup    ls_goods.ServiceGroup
	Ls_brandServiceGroup    ls_brand.ServiceGroup
	GoodsClassServiceGroup  goodsClass.ServiceGroup
	Li_supplierServiceGroup li_supplier.ServiceGroup
}
