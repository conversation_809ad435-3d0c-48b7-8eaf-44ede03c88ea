package ls_brand

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ls_brand"
	ls_brandReq "github.com/flipped-aurora/gin-vue-admin/server/model/ls_brand/request"
	"gorm.io/gorm"
)

type LsBrandService struct{}

// CreateLsBrand 创建lsBrand表记录
// Author [yourname](https://github.com/yourname)
func (lsBrandService *LsBrandService) CreateLsBrand(lsBrand *ls_brand.LsBrand) (err error) {
	err = global.GVA_DB.Create(lsBrand).Error
	return err
}

// DeleteLsBrand 删除lsBrand表记录
// Author [yourname](https://github.com/yourname)
func (lsBrandService *LsBrandService) DeleteLsBrand(ID string, userID uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&ls_brand.LsBrand{}).Where("id = ?", ID).Update("deleted_by", userID).Error; err != nil {
			return err
		}
		if err = tx.Delete(&ls_brand.LsBrand{}, "id = ?", ID).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// DeleteLsBrandByIds 批量删除lsBrand表记录
// Author [yourname](https://github.com/yourname)
func (lsBrandService *LsBrandService) DeleteLsBrandByIds(IDs []string, deleted_by uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&ls_brand.LsBrand{}).Where("id in ?", IDs).Update("deleted_by", deleted_by).Error; err != nil {
			return err
		}
		if err := tx.Where("id in ?", IDs).Delete(&ls_brand.LsBrand{}).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateLsBrand 更新lsBrand表记录
// Author [yourname](https://github.com/yourname)
func (lsBrandService *LsBrandService) UpdateLsBrand(lsBrand ls_brand.LsBrand) (err error) {
	err = global.GVA_DB.Model(&ls_brand.LsBrand{}).Where("id = ?", lsBrand.ID).Updates(&lsBrand).Error
	return err
}

// GetLsBrand 根据ID获取lsBrand表记录
// Author [yourname](https://github.com/yourname)
func (lsBrandService *LsBrandService) GetLsBrand(ID string) (lsBrand ls_brand.LsBrand, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&lsBrand).Error
	return
}

// GetLsBrandInfoList 分页获取lsBrand表记录
// Author [yourname](https://github.com/yourname)
func (lsBrandService *LsBrandService) GetLsBrandInfoList(info ls_brandReq.LsBrandSearch) (list []ls_brand.LsBrand, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&ls_brand.LsBrand{})
	var lsBrands []ls_brand.LsBrand
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.BrandName != nil && *info.BrandName != "" {
		db = db.Where("brand_name = ?", *info.BrandName)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&lsBrands).Error
	return lsBrands, total, err
}
func (lsBrandService *LsBrandService) GetLsBrandPublic() {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

func (lsBrandService *LsBrandService) GetAllLsBrandInfoList(info ls_brandReq.LsBrandSearch) (list []ls_brand.LsBrand, err error) {

	db := global.GVA_DB.Model(&ls_brand.LsBrand{})

	var lsBrands []ls_brand.LsBrand

	err = db.Find(&lsBrands).Error

	return lsBrands, err
}
