package ls_goods

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/lsGoodSpec"
	"github.com/flipped-aurora/gin-vue-admin/server/model/lsGoodSpecValueDetail"
	"github.com/flipped-aurora/gin-vue-admin/server/model/lsGoodsSpecValue"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ls_goods"
	ls_goodsReq "github.com/flipped-aurora/gin-vue-admin/server/model/ls_goods/request"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type LsGoodsService struct{}

// CreateLsGoods 创建lsGoods表记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsService *LsGoodsService) CreateLsGoods(goods *ls_goodsReq.Goods) (err error) {
	var lsGoods ls_goods.LsGoods
	var goodSpec lsGoodSpec.LsGoodSpec
	var goodSpecValue lsGoodsSpecValue.LsGoodSpecValue
	var goodSpecValueDetail lsGoodSpecValueDetail.LsGoodSpecValueDetail

	lsGoods = ls_goods.LsGoods{
		GoodType:              goods.GoodType,
		GoodCode:              uuid.NewString(),
		GoodName:              goods.GoodName,
		VideoEnable:           goods.VideoEnable,
		VideoSource:           goods.VideoSource,
		VideoUrl:              goods.VideoURL,
		VideoCover:            goods.VideoCover,
		GoodBrandId:           goods.GoodBrandID,
		GoodUnit:              goods.GoodUnit,
		GoodSupplier:          goods.GoodSupplier,
		GoodSpec:              goods.GoodSpec,
		Logistics:             goods.Logistics,
		ShippingFee:           goods.ShippingFee,
		GoodDetail:            goods.GoodDetail,
		ShippingFeeTemplement: goods.ShippingFeeTemplement,
		StockLimit:            goods.StockLimit,
		VirtualSale:           goods.VirtualSale,
		VirtualPageView:       goods.VirtualPageView,
		ServiceGuarantee:      goods.ServiceGuarantee,
		QuotaCode:             goods.QuotaCode,
		QuotaCount:            goods.QuotaCount,
		GoodStatus:            goods.GoodStatus,
		CreatedBy:             goods.CreatedBy,
		UpdatedBy:             goods.UpdatedBy,
		DeletedBy:             goods.DeletedBy,
	}

	err = global.GVA_DB.Create(&lsGoods).Error

	for _, detail := range goods.MenuBtn {
		goodSpec = lsGoodSpec.LsGoodSpec{
			Spec:   detail.Key,
			GoodID: lsGoods.ID,
		}
		global.GVA_DB.Create(&goodSpec)

		goodSpecValue = lsGoodsSpecValue.LsGoodSpecValue{
			GoodID: lsGoods.ID,
			SpecID: goodSpec.ID,
			Value:  detail.GoodSpecValue,
		}
		global.GVA_DB.Create(&goodSpecValue)

		goodSpecValueDetail = lsGoodSpecValueDetail.LsGoodSpecValueDetail{
			GoodSpecValue: detail.GoodSpecValue,
			GoodSpecImage: detail.GoodSpecImage,
			GoodSpecPrice: detail.GoodPrice,
			MarketPrice:   detail.MarketPrice,
			CostPrice:     detail.CostPrice,
			Stock:         detail.Stock,
			Volume:        detail.Volume,
			Weight:        detail.Weight,
			BarCode:       detail.BarCode,
			GoodID:        lsGoods.ID,
			GoodSpecName:  detail.GoodSpecValue,
		}
		global.GVA_DB.Create(&goodSpecValueDetail)
	}

	return err
}

// DeleteLsGoods 删除lsGoods表记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsService *LsGoodsService) DeleteLsGoods(ID string, userID uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&ls_goods.LsGoods{}).Where("id = ?", ID).Update("deleted_by", userID).Error; err != nil {
			return err
		}
		if err = tx.Delete(&ls_goods.LsGoods{}, "id = ?", ID).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// DeleteLsGoodsByIds 批量删除lsGoods表记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsService *LsGoodsService) DeleteLsGoodsByIds(IDs []string, deleted_by uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&ls_goods.LsGoods{}).Where("id in ?", IDs).Update("deleted_by", deleted_by).Error; err != nil {
			return err
		}
		if err := tx.Where("id in ?", IDs).Delete(&ls_goods.LsGoods{}).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateLsGoods 更新lsGoods表记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsService *LsGoodsService) UpdateLsGoods(lsGoods ls_goods.LsGoods) (err error) {
	err = global.GVA_DB.Model(&ls_goods.LsGoods{}).Where("id = ?", lsGoods.ID).Updates(&lsGoods).Error
	return err
}

// GetLsGoods 根据ID获取lsGoods表记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsService *LsGoodsService) GetLsGoods(ID string) (lsGoods ls_goods.LsGoods, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&lsGoods).Error
	return
}

// GetLsGoodsInfoList 分页获取lsGoods表记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsService *LsGoodsService) GetLsGoodsInfoList(info ls_goodsReq.LsGoodsSearch) (list []ls_goods.LsGoods, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&ls_goods.LsGoods{})
	var lsGoodss []ls_goods.LsGoods
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.GoodName != nil && *info.GoodName != "" {
		db = db.Where("good_name LIKE ?", "%"+*info.GoodName+"%")
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&lsGoodss).Error
	return lsGoodss, total, err
}
func (lsGoodsService *LsGoodsService) GetLsGoodsPublic() {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
