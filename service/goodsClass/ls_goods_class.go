package goodsClass

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/goodsClass"
	goodsClassReq "github.com/flipped-aurora/gin-vue-admin/server/model/goodsClass/request"
	"gorm.io/gorm"
)

type LsGoodsClassService struct{}

// CreateLsGoodsClass 创建商品分类记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsClassService *LsGoodsClassService) CreateLsGoodsClass(lsGoodsClass *goodsClass.LsGoodsClass) (err error) {
	err = global.GVA_DB.Create(lsGoodsClass).Error
	return err
}

// DeleteLsGoodsClass 删除商品分类记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsClassService *LsGoodsClassService) DeleteLsGoodsClass(ID string, userID uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&goodsClass.LsGoodsClass{}).Where("id = ?", ID).Update("deleted_by", userID).Error; err != nil {
			return err
		}
		if err = tx.Delete(&goodsClass.LsGoodsClass{}, "id = ?", ID).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// DeleteLsGoodsClassByIds 批量删除商品分类记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsClassService *LsGoodsClassService) DeleteLsGoodsClassByIds(IDs []string, deleted_by uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&goodsClass.LsGoodsClass{}).Where("id in ?", IDs).Update("deleted_by", deleted_by).Error; err != nil {
			return err
		}
		if err := tx.Where("id in ?", IDs).Delete(&goodsClass.LsGoodsClass{}).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateLsGoodsClass 更新商品分类记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsClassService *LsGoodsClassService) UpdateLsGoodsClass(lsGoodsClass goodsClass.LsGoodsClass) (err error) {
	err = global.GVA_DB.Model(&goodsClass.LsGoodsClass{}).Where("id = ?", lsGoodsClass.ID).Updates(&lsGoodsClass).Error
	return err
}

// GetLsGoodsClass 根据ID获取商品分类记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsClassService *LsGoodsClassService) GetLsGoodsClass(ID string) (lsGoodsClass goodsClass.LsGoodsClass, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&lsGoodsClass).Error
	return
}

// GetLsGoodsClassInfoList 分页获取商品分类记录
// Author [yourname](https://github.com/yourname)
func (lsGoodsClassService *LsGoodsClassService) GetLsGoodsClassInfoList(info goodsClassReq.LsGoodsClassSearch) (list []goodsClass.LsGoodsClass, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&goodsClass.LsGoodsClass{})
	var lsGoodsClasss []goodsClass.LsGoodsClass
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.ClassName != nil && *info.ClassName != "" {
		db = db.Where("class_name LIKE ?", "%"+*info.ClassName+"%")
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = global.GVA_DB.Where("pid=?", 0).Find(&lsGoodsClasss).Error
	childrenList := lsGoodsClassService.GetLsGoodsClassChildrenList(global.GVA_DB, lsGoodsClasss)
	return childrenList, total, err
}
func (lsGoodsClassService *LsGoodsClassService) GetLsGoodsClassChildrenList(db *gorm.DB, class []goodsClass.LsGoodsClass) []goodsClass.LsGoodsClass {

	var IsGoodsClass []goodsClass.LsGoodsClass
	var g []goodsClass.LsGoodsClass
	for _, lsGoodsClass := range class {
		db.Where("pid=?", lsGoodsClass.ID).Find(&IsGoodsClass)
		g = append(g, goodsClass.LsGoodsClass{
			GVA_MODEL: global.GVA_MODEL{
				ID:        lsGoodsClass.ID,
				CreatedAt: lsGoodsClass.CreatedAt,
				UpdatedAt: lsGoodsClass.UpdatedAt,
				DeletedAt: lsGoodsClass.DeletedAt,
			},
			ClassName: lsGoodsClass.ClassName,
			Pid:       lsGoodsClass.Pid,
			ClassIcon: lsGoodsClass.ClassIcon,
			Sort:      lsGoodsClass.Sort,
			Enable:    lsGoodsClass.Enable,
			CreatedBy: lsGoodsClass.CreatedBy,
			UpdatedBy: lsGoodsClass.UpdatedBy,
			DeletedBy: lsGoodsClass.DeletedBy,
			Children:  lsGoodsClassService.GetLsGoodsClassChildrenList(db, IsGoodsClass),
		})
	}
	return g
}
func (lsGoodsClassService *LsGoodsClassService) GetLsGoodsClassPublic() {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
