package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/goodsClass"
	"github.com/flipped-aurora/gin-vue-admin/server/model/li_supplier"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ls_brand"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ls_goods"
)

func bizModel() error {
	db := global.GVA_DB
	err := db.AutoMigrate(ls_goods.LsGoods{}, ls_brand.LsBrand{}, goodsClass.LsGoodsClass{}, li_supplier.LsSupplier{})
	if err != nil {
		return err
	}
	return nil
}
