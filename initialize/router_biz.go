package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	holder(publicGroup, privateGroup)
	{
		ls_goodsRouter := router.RouterGroupApp.Ls_goods
		ls_goodsRouter.InitLsGoodsRouter(privateGroup, publicGroup)
	}
	{
		ls_brandRouter := router.RouterGroupApp.Ls_brand
		ls_brandRouter.InitLsBrandRouter(privateGroup, publicGroup)
	}
	{
		goodsClassRouter := router.RouterGroupApp.GoodsClass
		goodsClassRouter.InitLsGoodsClassRouter(privateGroup, publicGroup)
	} // 占位方法，保证文件可以正确加载，避免go空变量检测报错，请勿删除。
	{
		li_supplierRouter := router.RouterGroupApp.Li_supplier
		li_supplierRouter.InitLsSupplierRouter(privateGroup, publicGroup)
	}
}
